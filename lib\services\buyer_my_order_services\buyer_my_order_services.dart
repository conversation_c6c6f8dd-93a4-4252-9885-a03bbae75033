//happy

import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/seller_all_order_response/tracking_detail_response.dart';
import 'package:swadesic/model/seller_all_order_response/return_tracking_detail_ressponse.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class BuyerMyOrderServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  BuyerMyOrderServices() {
    httpService = HttpService();
  }

  // endregion


  // region Get Buyer My Order
  // Future<BuyerMyOrderResponse>getBuyerMyOrder() async {
  //   final String data = await rootBundle.loadString('assets/buyer_order.json');
  //   Map<String, dynamic> jsonResponse = json.decode(data);
  //   // return response;
  //   return BuyerMyOrderResponse.fromJson(jsonResponse);
  // }
  Future<GetOrderResponse>getBuyerMyOrder({ String? orderNumber}) async {
    // get body [for POST request]


    var url = "";
    if(orderNumber==null){
      url = "${AppConstants.getBuyerMyOrder}${AppConstants.appData.userId}/";
    }
    else{
      url = "${AppConstants.getBuyerMyOrder}${AppConstants.appData.userId}/$orderNumber/";
    }
    //print(url);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
        response = await httpService.getApiCall(url);
    // return response;

    return GetOrderResponse.fromJson(response);
  }
  // endregion

  // region Cancel order
  cancelOrder(List<String> subOrderNumbers,String cancelReason) async {
    // get body [for POST request]
    List<Map> data = [] ;

    for(var number in subOrderNumbers){
      data.add(
          {
            "suborder_number":number,
            "status":"ORDER_CANCELLED",
            "cancellation_reason":cancelReason,
            "cancelled_by":"buyer"
          }
      );

    }

    var body ={
      "suborder_list":data
    };
    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.buyerSellerStatus);
    // return response;

    // return BuyerMyOrderResponse.fromJson(response);
  }
// endregion


  //region Get tracking detail
  Future<TrackingDetailResponse>getTrackingDetail({required String packageNumber})
  async {
    // get body [for POST request]
    var url = "${AppConstants.getTrackingDetail}$packageNumber/";
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.getApiCall(url);

    return TrackingDetailResponse.fromJson(response);

  }
  //endregion


  // region Return order
  returnOrder({
    required List<String> subOrderNumberList,
    required String reason,
    String? returnConditionsJson
  }) async {
    // get body [for POST request]

    List<Map> data = [] ;

    for(var number in subOrderNumberList){
      Map<String, dynamic> suborderData = {
        "suborder_number": number,
        "status": "RETURN_REQUESTED",
        "return_reason": reason,
      };

      // Add return conditions JSON if provided
      if (returnConditionsJson != null) {
        suborderData["return_conditions_json"] = returnConditionsJson;
      }

      data.add(suborderData);
    }

    var body = {
      "suborder_list": data
    };






    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.buyerSellerStatus);
    // return response;

    // return BuyerMyOrderResponse.fromJson(response);
  }
// endregion


  // region Delivery failed
  deliveryFailed(String subOrderNumber) async {
    // get body [for POST request]

    var body = {
      "suborder_list":[
        {"suborder_number":subOrderNumber,"status":"DELIVERY_FAILED"
        }
      ]
    };
    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.buyerSellerStatus);
    // return response;

    // return BuyerMyOrderResponse.fromJson(response);
  }
// endregion


  // region Buyer Need resolution
  buyerNeedResolution({
    required String orderNumber,
    required String subOrderNumberList,
    String? packageNumber,
    required String subOrderStatus,
    required String escalationReason,
    required String notes,
    required String storeReference




  }) async {
    // get body [for POST request]
    var body = {

      "order_reference": orderNumber,
      "store_reference": storeReference,
      "user_reference": BuyerHomeBloc.userDetailsResponse.userDetail!.userReference!,
      "suborder_reference":subOrderNumberList,
      "package_reference": packageNumber,
      "order_status": subOrderStatus,
      "escalation_type": escalationReason,
      "escalation_description": notes

    };
    //print(body);
    // endregion
    Map<String, dynamic> response;
    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.needResolution);
    // return response;

    // //print(response);
    // return BuyerMyOrderResponse.fromJson(response);
  }
// endregion



  // region Refund amount calculation
  // Future<BuyerMyOrderResponse>getBuyerMyOrder() async {
  //   final String data = await rootBundle.loadString('assets/buyer_order.json');
  //   Map<String, dynamic> jsonResponse = json.decode(data);
  //   // return response;
  //   return BuyerMyOrderResponse.fromJson(jsonResponse);
  // }
  Future<RefundAmountCalculationResponse>refundAmountCalculation({ required String orderNumber,required List<String> subOrderReferenceList}) async {
    // get body [for POST request]

    var body = {
      "order_number": orderNumber,
      "suborder_numbers":subOrderReferenceList
    };

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.refundAmountCalculationBuyerView);
    // return response;

    return RefundAmountCalculationResponse.fromJson(response);
  }
// endregion


  // region Cancel return request
  cancelReturnRequest({required List<String> subOrderNumbers}) async {
    // get body [for POST request]
    List<Map> data = [] ;

    for(var value in subOrderNumbers){
      data.add(
          {"suborder_number":value,

            "status":"RETURN_REQUEST_CANCELLED"
          }
      );

    }

    var body ={
      "suborder_list":data
    };
    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.buyerSellerStatus);
    // return response;

    // return BuyerMyOrderResponse.fromJson(response);
  }
// endregion

  //region Get Return tracking detail
  Future<ReturnTrackingDetailResponse> getReturnTrackingDetail({required String packageNumber}) async {
    // get body [for POST request]
    var url = "${AppConstants.returnTracking}$packageNumber/";
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.getApiCall(url);

    return ReturnTrackingDetailResponse.fromJson(response);
  }
  //endregion



}
