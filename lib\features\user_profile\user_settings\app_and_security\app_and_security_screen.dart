import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import 'app_and_security_bloc.dart';

//region App and security Screen
class AppAndSecurityScreen extends StatefulWidget {
  const AppAndSecurityScreen({Key? key}) : super(key: key);

  @override
  State<AppAndSecurityScreen> createState() => _AppAndSecurityScreenState();
}
//endregion

class _AppAndSecurityScreenState extends State<AppAndSecurityScreen> {
  //region Build
  late AppAndSecurityBloc appAndSecurityBloc;
  //endregion
  //region Init
  @override
  void initState() {
    appAndSecurityBloc = AppAndSecurityBloc(context);
    appAndSecurityBloc.init();
    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    appAndSecurityBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.appAndSecurity,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          appUpdateAndVersion(),
          securityAndTerms(),
          changeAppFont(),
          deleteAccount(),
          logout(),
        ],
      ),
    );
  }
//endregion

//region App,update and version
  Widget appUpdateAndVersion() {
    return Column(
      children: [
        //App
        Container(
          alignment: Alignment.centerLeft,
          child: Text(
            AppStrings.app,
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          ),
          // child: appText(AppStrings.app,fontWeight: FontWeight.w700,fontSize: 14,fontFamily: AppConstants.rRegular,
          // height: 1.19,
          //   color: AppColors.appBlack,
          //
          // ),
        ),

        //Update the latest app
        StreamBuilder<AppAndSecurityState>(
            stream: appAndSecurityBloc.refreshCtrl.stream,
            initialData: AppAndSecurityState.Loading,
            builder: (context, snapshot) {
              if (snapshot.data == AppAndSecurityState.Success) {
                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 15),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        appAndSecurityBloc.availableUpdate
                            ? AppStrings.updateTheLatestApp
                            : AppStrings.appIsUpToDate,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),
                      Visibility(
                        visible: appAndSecurityBloc.availableUpdate,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          margin: const EdgeInsets.symmetric(vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.brandBlack,
                              borderRadius: BorderRadius.circular(20)),
                          child: InkWell(
                              onTap: () {
                                CommonMethods.appUpdateCheck();
                              },
                              child: Text(
                                AppStrings.update,
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.appWhite),
                              )
                              // child: appText(AppStrings.update,fontWeight: FontWeight.w700,fontSize: 16,fontFamily: AppConstants.rRegular,
                              //     height: 1.19,
                              //     color: AppColors.white,
                              //
                              //     maxLine: 1
                              // ),
                              ),
                        ),
                      )
                    ],
                  ),
                );
              }
              return const SizedBox();
            }),
        //App version
        StreamBuilder<AppAndSecurityState>(
            stream: appAndSecurityBloc.refreshCtrl.stream,
            initialData: AppAndSecurityState.Loading,
            builder: (context, snapshot) {
              if (snapshot.data == AppAndSecurityState.Success) {
                return Container(
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        AppStrings.appVersion,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),
                      Text(
                        appAndSecurityBloc.packageInfo.version,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox();
            }),
      ],
    );
  }
//endregion

//region Security and terms
  Widget securityAndTerms() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Security
        // Text(AppStrings.security,style: AppTextStyle.access0(textColor: AppColors.appBlack),),
        //
        // verticalSizedBox(15),
        //Terms
        InkWell(
          onTap: () {
            CommonMethods.openAppWebView(
                webUrl: AppConstants.appTermsAndConditionBuyer,
                context: context);
          },
          child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                AppStrings.termsAndCondition,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              )),
        ),

        //Divider
        divider(),
        //privacy agreement
        InkWell(
          onTap: () {
            CommonMethods.openAppWebView(
                webUrl: AppConstants.appPrivacyAndPolicy, context: context);
          },
          child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              width: double.infinity,
              child: Text(
                AppStrings.privacyAgreement,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              )),
        ),
      ],
    );
  }
//endregion

  //region Change app font
  Widget changeAppFont() {
    return InkWell(
        onTap: () {
          appAndSecurityBloc.goToAppFontScreen();
        },
        child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              AppStrings.appFontSize,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            )));
  }
//endregion

  //region Delete account
  Widget deleteAccount() {
    return InkWell(
        onTap: () {
          appAndSecurityBloc.onTapDeleteAccount();
        },
        child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              AppStrings.deleteAccount,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            )));
  }
//endregion

//region Logout
  Widget logout() {
    return InkWell(
        onTap: () {
          appAndSecurityBloc.openDialog();
        },
        child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              "Logout",
              style: AppTextStyle.access0(textColor: AppColors.red),
            )));
  }
//endregion
}
